import { Injectable, UnauthorizedException, BadRequestException, Logger, InternalServerErrorException, Body } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { User } from '../entities/user.entity';
import { LoginDto } from '../dto/auth/login.dto';
import { RegisterDto } from '../dto/auth/register.dto';
import { ForgotPasswordDto, ResetPasswordDto } from '../dto/auth/forgot-password.dto';
import { RequestTwoFactorDto, TwoFactorDto } from '../dto/auth/two-factor.dto';
import * as speakeasy from "speakeasy";
import * as qrcode from "qrcode";
import * as bcrypt from "bcryptjs";
import { MailerService } from '@nestjs-modules/mailer';
import { join } from 'path';
import { assetsDir } from "../app.module";
import { Request } from "express";
import axios from "axios";

export interface JwtPayload {
  email: string;
  sub: string;
  roles?: string[];
}

export interface AuthResponse {
  access_token: string;
  user: {
    user_id: string;
    email: string;
    first_name: string;
    last_name: string;
    two_factor_enabled: boolean,
    roles?: string[];
    isStaff?: boolean;
  };
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private mailerService: MailerService,
  ) { }

  async validateUser(email: string, password: string): Promise<User | null> {
    this.logger.log(`Validating user: ${email}`);

    const user = await this.usersService.findByEmail(email);
    this.logger.log(`User found: ${user ? 'YES' : 'NO'}`);

    if (user) {
      const isPasswordValid = await this.usersService.validatePassword(password, user.password);
      this.logger.log(`Password valid: ${isPasswordValid ? 'YES' : 'NO'}`);

      if (isPasswordValid) {
        return user;
      }
    }

    return null;
  }

  async login(loginDto: LoginDto, req: Request): Promise<AuthResponse> {
    try {
      this.logger.log(`Login attempt for email: ${loginDto.email}`);

      const user = await this.validateUser(loginDto.email, loginDto.password);
      this.logger.log(`Validation result for ${loginDto.email}: ${user ? 'SUCCESS' : 'FAILED'}`);

      if (!user) {
        this.logger.warn(`Invalid credentials for email: ${loginDto.email}`);
        throw new UnauthorizedException('Invalid email or password');
      }

      if (user.status !== 'active') {
        this.logger.warn(`Inactive account login attempt: ${loginDto.email}`);
        throw new UnauthorizedException('Account is not active');
      }

      // Update last login
      await this.usersService.updateLastLogin(user.user_id);

      const payload: JwtPayload = {
        email: user.email,
        sub: user.user_id,
        roles: user.roles?.map(role => role.name) || [],
      };

      // Determine if user is staff based on roles
      const staffRoles = ['admin', 'administrator', 'staff', 'moderator', 'manager'];
      const isStaff = user.roles?.some(role => 
        staffRoles.includes(role.name.toLowerCase())
      ) || false;

      const 

      return {
        access_token: this.jwtService.sign(payload),
        user: {
          user_id: user.user_id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          two_factor_enabled: user.two_factor_enabled,
          roles: user.roles?.map(role => role.name) || [],
          isStaff: isStaff,
        },
      };
    } catch (error) {
      this.logger.error(`Login failed for ${loginDto.email}: ${error.message}`);
      throw error;
    }
  }

  async register(registerDto: RegisterDto): Promise<AuthResponse> {
    try {
      const user = await this.usersService.create(registerDto);

      const payload: JwtPayload = {
        email: user.email,
        sub: user.user_id,
        roles: user.roles?.map(role => role.name) || [],
      };

      // Determine if user is staff based on roles
      const staffRoles = ['admin', 'administrator', 'staff', 'moderator', 'manager'];
      const isStaff = user.roles?.some(role => 
        staffRoles.includes(role.name.toLowerCase())
      ) || false;

      return {
        access_token: this.jwtService.sign(payload),
        user: {
          user_id: user.user_id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          two_factor_enabled: user.two_factor_enabled,
          roles: user.roles?.map(role => role.name) || [],
          isStaff: isStaff,
        },
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async validateJwtPayload(payload: JwtPayload): Promise<User | null> {
    return this.usersService.findByEmail(payload.email);
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{ message: string }> {
    const user = await this.usersService.findByEmail(forgotPasswordDto.email);
    if (!user) {
      // Don't reveal if email exists or not
      return { message: 'If the email exists, a password reset link has been sent.' };
    }

    await this.generateTwoFactorCode(user.user_id, 'reset');

    return { message: 'If the email exists, a password reset link has been sent.' };
  }

  /**
   * Reset password, triggered with `forgotPassword`
   * @param email 
   * @param code - Verified with verifyTwoFactorCode
   * @param newPassword - Verified with confirm password
   * @returns message
   */
  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    const user = await this.usersService.findById(resetPasswordDto.user_id);
    if (!user) {
      throw new BadRequestException('Invalid reset code');
    }

    // Compare existing password and new password
    if (await this.usersService.validatePassword(resetPasswordDto.new_password, user.password)) {
      throw new BadRequestException("New password cannot be the same as the current password");
    }

    await this.usersService.updatePassword(user.user_id, resetPasswordDto.new_password);
    await this.usersService.clearTempTwoFactorCode(user.user_id);
    await this.usersService.clearTwoFactorCode(user.user_id);

    this.mailerService.sendMail({
      to: user.email,
      subject: 'Password Reset - MACRA Digital Portal',
      template: 'reset',
      context: {
        userName: user.first_name,
        year: new Date().getFullYear(),
        loginUrl: `${process.env.FRONTEND_URL}/auth/login`
      },
      attachments: [
        {
          filename: 'macra-logo.png',
          path: join(assetsDir, 'macra-logo.png'),
          cid: 'logo@macra'
        }
      ]
    }).catch((error) => {
      this.logger.error('An error occurred while mailing successful password reset', error);
    });

    return { message: `Password reset successfully for ${user.email}! Please login with your new password.` };
  }

  async setupTwoFactorAuth(@Body() requestTwoFactorDto: RequestTwoFactorDto): Promise<{ otpAuthUrl: string, qrCodeDataUrl: string, secret: string, message: string }> {
    const userId = requestTwoFactorDto.user_id;
    const user = await this.usersService.findById(userId);
    if (!user) {
      this.logger.warn(`First time 2FA setup failed: User ID ${userId} not found!`);
      throw new BadRequestException('User not found');
    }
    if (user.two_factor_enabled) {
      return {
        otpAuthUrl: '',
        qrCodeDataUrl: '',
        secret: '',
        message: `Two-factor authentication is already enabled for ${user.email}.\n\n Redirecting to login..`,
      };
    }
    const generateCode = await this.generateTwoFactorCode(requestTwoFactorDto.user_id, 'verify');
    const otpAuthUrl = generateCode.otpAuthUrl;
    const secret = generateCode.secret;
    const qrCodeDataUrl = await qrcode.toDataURL(otpAuthUrl);

    return {
      otpAuthUrl,
      qrCodeDataUrl,
      secret: secret,
      message: `Two factor authentication initiation for ${user.email} successful! Please check your email for the verification link.`
    };
  }

  /**
   * 
   * @param userId 
   * @param action  can be either `reset`, `login`, `verify`. URL defaults to `verify-2fa`
   * @returns message
   */
  async generateTwoFactorCode(userId: string, action: string): Promise<{ message: string, otpAuthUrl: string, hashedToken: string, secret: string }> {
    const user = await this.usersService.findById(userId);
    if (!user) {
      this.logger.warn(`2FA attempt failed: User ID ${userId} not found!`);
      throw new BadRequestException('User not found');
    }

    const secret = speakeasy.generateSecret({
      name: 'MACRA Digital Portal',
      length: 16,
    });
    const token = speakeasy.totp({
      secret: secret.base32,
      encoding: 'base32'
    });
    const hashedToken = await bcrypt.hash(token, 8);
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
    const url_redirect = (action === 'reset') ? 'reset-password' : ((action === 'login') ? 'login' : 'verify-2fa');
    const msg = (action === 'reset') ? `You are receiving this email because we received a password reset request for your account.\nIgnore if received in error` : ((action === 'login') ? `You are receiving this email because we received a login request for your account.\nIgnore if received in error` : `You are receiving this email because we received a 2FA verification request for your account.\nIgnore if received in error`);
    const template = (action === 'reset') ? 'reset' : ((action === 'login') ? '2fa' : '2fa');
    if (!secret.otpauth_url) {
      throw new InternalServerErrorException('Failed to generate OTP URL');
    }

    try {

      if (action === 'login') {
        await this.usersService.setTwoFactorCode(userId, hashedToken, expiresAt);
      } else {
        await this.usersService.setTwoFactorCodeTempReset(userId, secret.base32, hashedToken, expiresAt);
      }
    } catch (error) {
      this.logger.error('An error occurred while setting the 2FA Code', error);
    }
    
    this.mailerService.sendMail({
      to: user.email,
      subject: 'Verify OTP - MACRA Digital Portal',
      template: template,
      context: {
        name: user.first_name,
        message: msg,
        year: new Date().getFullYear(),
        verifyUrl: `${process.env.FRONTEND_URL}/auth/${url_redirect}?i=${encodeURIComponent(user.user_id)}&unique=${encodeURIComponent(secret.base32)}&c=${encodeURIComponent(token)}`
      },
      attachments: [
        {
          filename: 'macra-logo.png',
          path: join(assetsDir, 'macra-logo.png'),
          cid: 'logo@macra'
        }
      ]
    }).catch(error => {
      this.logger.error('An error occurred while mailing OTP', error);
    });


    return { message: '2FA code has been sent', otpAuthUrl: secret.otpauth_url, hashedToken: hashedToken, secret: secret.base32 };
  }

  async verifyTwoFactorCode(twoFactorDto: TwoFactorDto): Promise<AuthResponse | { message: string }> {
    const user = await this.usersService.findById(twoFactorDto.user_id);
    if (!user) {
      throw new BadRequestException('Invalid user account details!');
    }

    if (!user.two_factor_code || !user.two_factor_temp || user.two_factor_temp !== twoFactorDto.unique) {
      this.logger.warn(`Invalid unique code for user ${user.email}`);
      throw new BadRequestException('Invalid verification link!');
    }
    if (!user.two_factor_next_verification || user.two_factor_next_verification < new Date()) {
      if (user.two_factor_next_verification) {
        this.logger.warn(`Expired code. Expired on: `,user.two_factor_next_verification);
      }
      
      throw new BadRequestException('Expired verification link!');
    }
    const compareToken = await bcrypt.compare(twoFactorDto.code, user.two_factor_code);
    if (!compareToken) {
      throw new BadRequestException('Invalid verification code');
    }

    try {
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
      await this.usersService.clearTwoFactorCode(user.user_id);
      await this.usersService.setTwoFactorCode(user.user_id, twoFactorDto.code, expiresAt);
    } catch (error) {
      this.logger.error('Failed to clear two factor code', error);
    }
    
    const payload: JwtPayload = { sub: user.user_id, email: user.email, roles: user.roles?.map(r => r.name) };
    return {
      access_token: (user.two_factor_enabled) ? this.jwtService.sign(payload) : '',
      user: {
        user_id: user.user_id, email: user.email, first_name: user.first_name, last_name: user.last_name,
        two_factor_enabled: user.two_factor_enabled, roles: payload.roles
      },
      message: (user.two_factor_enabled) ? 'OTP verified successfully' : `Two-factor authentication enabled for ${user.email}!`
    };
  }

  async generateLoginDeviceDetails(req: Request): Promise<{ ip: string, country: string, city: string, userAgent: string }> {

    const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
    const userAgent = req.headers['user-agent'];
    const ip = ipAddress ? ipAddress.toString().split(',').pop() : '';
    const response = await axios.get(`http://ip-api.com/json/${ip}`);
    return {
      ip: ip ? ip : 'unknown',
      country: response.data.country ? response.data.country : 'unknown',
      city: response.data.city ? response.data.city : 'unknown',
      userAgent: userAgent ? userAgent : 'unknown',
    };
  }

}
