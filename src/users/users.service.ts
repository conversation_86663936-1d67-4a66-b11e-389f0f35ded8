import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserStatus } from '../entities/user.entity';
import { Role, RoleName } from '../entities/role.entity';
import { CreateUserDto } from '../dto/user/create-user.dto';
import { UpdateUserDto } from '../dto/user/update-user.dto';
import { UpdateProfileDto } from '../dto/user/update-profile.dto';
import { ChangePasswordDto } from '../dto/user/change-password.dto';
import * as bcrypt from 'bcryptjs';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Role)
    private rolesRepository: Repository<Role>,
  ) {}

  async findByEmail(email: string): Promise<User | null> {
    return this.usersRepository.findOne({
      where: { email },
      relations: ['roles'],
    });
  }

  async findById(id: string): Promise<User | null> {
    return this.usersRepository.findOne({
      where: { user_id: id },
      relations: ['roles'],
    });
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    // Check if user already exists
    const existingUser = await this.findByEmail(createUserDto.email);
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Validate roles if provided
    let roles: Role[] = [];
    if (createUserDto.role_ids && createUserDto.role_ids.length > 0) {
      const foundRoles = await this.rolesRepository.findByIds(createUserDto.role_ids);
      if (foundRoles.length !== createUserDto.role_ids.length) {
        throw new NotFoundException('One or more roles not found');
      }
      roles = foundRoles;
    } else {
      // Assign default customer role if no roles specified
      const defaultRole = await this.rolesRepository.findOne({
        where: { name: RoleName.CUSTOMER },
      });
      if (defaultRole) {
        roles = [defaultRole];
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(createUserDto.password, 12);

    // Create user
    const user = this.usersRepository.create({
      ...createUserDto,
      password: hashedPassword,
      status: createUserDto.status || UserStatus.ACTIVE,
      roles,
    });

    return this.usersRepository.save(user);
  }

  async validatePassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  async updateLastLogin(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      last_login: new Date(),
    });
  }

  async updatePassword(userId: string, newPassword: string): Promise<void> {
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    await this.usersRepository.update(userId, {
      password: hashedPassword,
    });
  }

  async setTwoFactorCode(userId: string, code: string, expiresAt: Date): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_code: code,
      two_factor_next_verification: expiresAt,
      two_factor_enabled: true,
      two_factor_temp: null as any,
    });
  }

  async setTempTwoFactorCode(userId: string, secret: string, expiresAt: Date): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_temp: secret,
      two_factor_enabled: false,
      two_factor_next_verification: expiresAt,
    });
  }

  async clearTempTwoFactorCode(userId:string): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_temp: null as any,
    });
  }

  async clearTwoFactorCode(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_code: null as any,
      two_factor_temp: null as any,
    });
  }

  async setTwoFactorCodeTempReset(userId: string, secret: string, code: string, expiresAt: Date): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_code: code,
      two_factor_temp: secret,
      two_factor_next_verification: expiresAt,
    });
  }

  async disableTwoFactor(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_enabled: false,
      two_factor_code: null as any,
      two_factor_temp: null as any,
    });
  }

  async enableTwoFactor(userId:string, expiresAt: Date): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_enabled: true,
      two_factor_next_verification:  expiresAt,
      two_factor_temp: null as any,
    });
  }
  async verifyEmail(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      email_verified_at: new Date(),
    });
  }

  async updateStatus(userId: string, status: UserStatus): Promise<void> {
    await this.usersRepository.update(userId, { status });
  }

  async findAll(query: PaginateQuery): Promise<PaginatedResult<User>> {
    console.log('UsersService: findAll called with query:', JSON.stringify(query, null, 2));

    // Build query builder for custom filtering
    const queryBuilder = this.usersRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'roles')
      .leftJoinAndSelect('user.department', 'department');

    // Apply custom filters
    if (query.filter) {
      // Department filter
      if (query.filter.department) {
        queryBuilder.andWhere('user.department_id = :departmentId', {
          departmentId: query.filter.department
        });
      }

      // Role filter
      if (query.filter.role) {
        queryBuilder.andWhere('roles.role_id = :roleId', {
          roleId: query.filter.role
        });
      }

      // Status filter
      if (query.filter.status) {
        queryBuilder.andWhere('user.status = :status', {
          status: query.filter.status
        });
      }
    }

    const config: PaginateConfig<User> = {
      sortableColumns: ['first_name', 'last_name', 'email', 'created_at', 'status'],
      searchableColumns: ['first_name', 'last_name', 'email'],
      defaultSortBy: [['created_at', 'DESC']],
      defaultLimit: 10,
      maxLimit: 100,
      filterableColumns: {
        status: true,
        department_id: true,
      },
      relations: ['roles', 'department'],
    };

    console.log('UsersService: Using config:', JSON.stringify(config, null, 2));

    const result = await paginate(query, queryBuilder, config);
    console.log('UsersService: Raw pagination result:', JSON.stringify(result, null, 2));

    const transformedResult = PaginationTransformer.transform<User>(result);
    console.log('UsersService: Transformed result meta:', JSON.stringify(transformedResult.meta, null, 2));

    return transformedResult;
  }

  async update(userId: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Validate roles if provided
    let roles: Role[] | undefined;
    if (updateUserDto.role_ids && updateUserDto.role_ids.length > 0) {
      const foundRoles = await this.rolesRepository.findByIds(updateUserDto.role_ids);
      if (foundRoles.length !== updateUserDto.role_ids.length) {
        throw new NotFoundException('One or more roles not found');
      }
      roles = foundRoles;
    }

    // Hash password if provided
    let hashedPassword: string | undefined;
    if (updateUserDto.password) {
      hashedPassword = await bcrypt.hash(updateUserDto.password, 12);
    }

    // Prepare update data (excluding role_ids which is handled separately)
    const { role_ids, ...updateDataWithoutRoles } = updateUserDto;
    const updateData: Partial<User> = {
      ...updateDataWithoutRoles,
      ...(hashedPassword && { password: hashedPassword }),
    };

    // Remove sensitive fields that shouldn't be updated directly
    const { password, two_factor_code, two_factor_next_verification, ...safeUpdateData } = updateData;

    // Update basic user fields
    Object.assign(user, safeUpdateData);

    // Update roles if provided
    if (roles) {
      user.roles = roles;
    }

    // Save the user with all updates including relations
    await this.usersRepository.save(user);

    // Return fresh user data with relations
    const updatedUser = await this.findById(userId);
    if (!updatedUser) {
      throw new NotFoundException('User not found after update');
    }
    return updatedUser;
  }

  async delete(userId: string): Promise<void> {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.usersRepository.softDelete(userId);
  }

  // Profile-specific methods
  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<User> {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if email is being changed and if it's already taken
    if (updateProfileDto.email && updateProfileDto.email !== user.email) {
      const existingUser = await this.findByEmail(updateProfileDto.email);
      if (existingUser) {
        throw new ConflictException('Email is already taken');
      }
    }

    await this.usersRepository.update(userId, updateProfileDto);
    const updatedUser = await this.findById(userId);
    if (!updatedUser) {
      throw new NotFoundException('User not found after update');
    }
    return updatedUser;
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<{ message: string }> {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await this.validatePassword(
      changePasswordDto.current_password,
      user.password
    );
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    // Check if new password matches confirmation
    if (changePasswordDto.new_password !== changePasswordDto.confirm_password) {
      throw new BadRequestException('New password and confirmation do not match');
    }

    // Update password
    await this.updatePassword(userId, changePasswordDto.new_password);

    return { message: 'Password changed successfully' };
  }

  async uploadAvatar(userId: string, file: Express.Multer.File): Promise<User> {
    console.log('UsersService: uploadAvatar called', { userId, file: file ? file.originalname : 'no file' });

    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    console.log('UsersService: File details', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      filename: file.filename,
      path: file.path
    });

    try {
      // Store the file as base64 in the database for simplicity
      // In production, you'd want to use a file storage service like AWS S3
      const base64Image = `data:${file.mimetype};base64,${file.buffer.toString('base64')}`;

      console.log('UsersService: Updating user with base64 image');
      await this.usersRepository.update(userId, {
        profile_image: base64Image,
      });

      const updatedUser = await this.findById(userId);
      if (!updatedUser) {
        throw new NotFoundException('User not found after update');
      }

      console.log('UsersService: Avatar upload successful');
      return updatedUser;
    } catch (error) {
      console.error('UsersService: Error uploading avatar', error);
      throw new BadRequestException('Failed to upload avatar');
    }
  }

  async removeAvatar(userId: string): Promise<User> {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.usersRepository.update(userId, {
      profile_image: null as any,
    });

    const updatedUser = await this.findById(userId);
    if (!updatedUser) {
      throw new NotFoundException('User not found after update');
    }
    return updatedUser;
  }

  async mailUser(userEmail: string): Promise<{ message: string }> {
    const user = await this.findByEmail(userEmail);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return { message: 'Email sent! Please check inbox' };
  }
}
